# 相机全屏显示优化总结

## 问题描述
在某些手机上，相机预览无法全屏展示，可能出现黑边、拉伸变形或显示不完整等问题。

## 优化方案

### 1. 优化预览尺寸选择算法 (`CameraPreviewSizeSelector.kt`)

**主要改进：**
- 改进了 `optimizedPreviewSize` 函数，增加了宽高比容差机制
- 新增 `bestFullScreenSize` 函数，专门为全屏显示选择最佳预览尺寸
- 按分辨率降序排列，优先选择高质量但不超过限制的尺寸
- 增加了对不同设备的适配支持

**关键特性：**
- 宽高比容差：允许0.1的比例差异，提高匹配成功率
- 智能分辨率选择：优先选择1080p、720p等常见高质量尺寸
- 设备适配：根据设备特性调整最大宽度和容差

### 2. 重构TextureView初始化逻辑 (`HomeScreen.kt`)

**主要改进：**
- 优化预览尺寸计算时机，避免重复计算
- 改进 `onSurfaceTextureSizeChanged` 处理逻辑
- 只在尺寸真正变化时重新启动预览

**关键特性：**
- 智能尺寸管理：只在必要时重新计算预览尺寸
- 更好的变换应用：确保变换矩阵在正确时机应用
- 错误处理：增加异常处理，提高稳定性

### 3. 创建设备适配器 (`CameraDisplayAdapter.kt`)

**主要功能：**
- 针对不同设备品牌提供特定的配置
- 智能计算变换矩阵以实现完美的全屏显示
- 处理设备特定的旋转和缩放问题

**设备特定配置：**
- **Samsung**: 旋转偏移-90°，额外缩放1.02倍
- **Huawei**: 旋转偏移-90°，额外缩放1.01倍
- **Xiaomi**: 旋转偏移-90°，无额外缩放
- **OPPO/OnePlus**: 旋转偏移-90°，额外缩放1.01倍
- **Vivo**: 旋转偏移-90°，额外缩放1.02倍

**关键算法：**
- 设备检测：自动识别设备品牌和型号
- 旋转补偿：精确计算传感器和显示旋转
- 缩放优化：使用CenterCrop策略确保全屏填充
- 镜像处理：正确处理前置摄像头镜像

### 4. 优化相机初始化 (`setupCamera`)

**主要改进：**
- 获取实际显示尺寸用于预览尺寸选择
- 使用新的 `bestFullScreenSize` 算法
- 增加默认尺寸备选方案
- 更好的错误处理

### 5. 简化变换矩阵计算

**主要改进：**
- 使用 `CameraDisplayAdapter` 统一处理变换计算
- 提供备用变换方案以防适配器失败
- 移除冗余的辅助函数，代码更简洁

## 技术细节

### 变换矩阵计算流程
1. 获取显示旋转信息
2. 计算传感器方向
3. 计算旋转补偿
4. 应用设备特定的旋转调整
5. 应用旋转变换
6. 应用镜像变换（仅前置摄像头）
7. 计算并应用缩放
8. 计算平移以居中

### 预览尺寸选择策略
1. 尝试完全匹配显示尺寸
2. 筛选宽高比接近的尺寸（容差范围内）
3. 在匹配尺寸中选择合适分辨率
4. 如果没有匹配的，选择最接近的尺寸

### 设备适配策略
- 检测设备品牌和型号
- 应用设备特定的配置参数
- 提供通用的备选方案

## 预期效果

1. **更好的全屏显示**：消除黑边，实现真正的全屏预览
2. **更广泛的设备兼容性**：针对主流Android设备品牌进行优化
3. **更稳定的性能**：减少预览尺寸重复计算，提高效率
4. **更好的用户体验**：预览画面更清晰，显示更准确

## 使用方法

优化后的代码会自动应用，无需额外配置。系统会：
1. 自动检测设备类型
2. 选择最佳预览尺寸
3. 应用适合的变换矩阵
4. 实现完美的全屏显示

### 📁 **修改的文件示例**

<augment_code_snippet path="app/src/main/java/com/mobile/anchor/app/ui/screens/home/<USER>" mode="EXCERPT">
```kotlin
AndroidView(
    modifier = Modifier.fillMaxSize(), factory = { ctx ->
        TextureView(ctx).apply {
            textureView = this
            // 优化的Surface监听器处理预览尺寸和变换
            surfaceTextureListener = object : TextureView.SurfaceTextureListener {
                override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                    // 智能的预览尺寸选择
                    if (previewSize == null) {
                        previewSize = CameraPreviewSizeSelector.bestFullScreenSize(availableSizes, width, height)
                    }
                    // 应用优化的变换矩阵
                    previewSize?.let { size -> updateTextureViewTransform(this@apply, size) }
                }
            }
        }
    })
```
</augment_code_snippet>

## 注意事项

1. ✅ **已修复**：移除了错误的 `scaleType` 属性（TextureView 不支持此属性）
2. 如果遇到新的设备兼容性问题，可以在 `CameraDisplayAdapter` 中添加设备特定配置
3. 备用变换方案确保即使适配器失败也能正常显示
4. 所有变更都向后兼容，不会影响现有功能
