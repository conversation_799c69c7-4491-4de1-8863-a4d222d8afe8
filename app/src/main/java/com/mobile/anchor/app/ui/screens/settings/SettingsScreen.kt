package com.mobile.anchor.app.ui.screens.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.mobile.anchor.app.BuildConfig
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.VersionBean
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.navigation.Screen
import com.mobile.anchor.app.ui.components.AnchorDangerButton
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.ConfirmDialog
import com.mobile.anchor.app.ui.components.LoadingDialog
import com.mobile.anchor.app.ui.components.UpdateDialog
import com.mobile.anchor.app.ui.components.UpdateProgressDialog
import com.mobile.anchor.app.ui.screens.about.AboutScreen
import com.mobile.anchor.app.ui.screens.blacklist.BlacklistScreen
import com.mobile.anchor.app.ui.screens.language.LanguageScreen
import com.mobile.anchor.app.ui.screens.webview.WebViewScreen
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.update.AppUpdateManager
import com.mobile.anchor.app.update.DownloadState
import com.mobile.anchor.app.update.UpdateState
import com.mobile.anchor.app.utils.Constants

/**
 * 设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen() {
    val context = LocalContext.current
    val navController = rememberNavController()
    val viewModel: SettingsViewModel = viewModel()
    val uiState by viewModel.uiState.collectAsState()
    var isCheckingVersion by remember { mutableStateOf(false) }
    // 更新管理器
    val updateManager = remember { AppUpdateManager.getInstance(context) }
    val updateState by updateManager.updateState.collectAsState()
    val downloadState by updateManager.downloadState.collectAsState()

    var showUpdateDialog by remember { mutableStateOf(false) }
    var showProgressDialog by remember { mutableStateOf(false) }
    var currentVersionInfo by remember {
        mutableStateOf<VersionBean?>(null)
    }
    var isUserTriggeredCheck by remember { mutableStateOf(false) }

    // 监听更新状态
    LaunchedEffect(updateState) {
        isCheckingVersion = false
        if (isUserTriggeredCheck) {
            when (val state = updateState) {
                is UpdateState.UpdateAvailable -> {
                    currentVersionInfo = state.versionInfo
                    showUpdateDialog = true
                    LogX.d("检测到新版本: ${state.versionInfo.update_tips}")
                }

                is UpdateState.CheckUpdateError -> {
                    LogX.e("检查更新失败: ${state.error}")
                }

                else -> {}
            }
        }
    }

    // 监听下载状态
    LaunchedEffect(downloadState) {
        when (val state = downloadState) {
            is DownloadState.Preparing, is DownloadState.Downloading -> {
                showProgressDialog = true
            }

            is DownloadState.Downloaded -> {
                showProgressDialog = false
                // 自动安装
                updateManager.installApk(state.filePath, context as? android.app.Activity)
            }

            is DownloadState.DownloadError -> {
                // 不自动关闭弹框，让用户手动点击取消或重试
                isCheckingVersion = false
                LogX.e("下载失败: ${state.error}")
            }

            is DownloadState.DownloadCancelled -> {
                // 不自动关闭弹框，让用户手动点击确定关闭
                isCheckingVersion = false
                LogX.d("下载已取消，重置检查状态")
            }

            else -> {}
        }
    }

    // 更新弹框
    currentVersionInfo?.let { versionInfo ->
        UpdateDialog(
            visible = showUpdateDialog && isUserTriggeredCheck,
            versionInfo = versionInfo,
            onDismiss = {
                showUpdateDialog = false
                updateManager.resetStates()
            },
            onConfirm = {
                showUpdateDialog = false
                updateManager.startDownload(versionInfo)
            },
            onCancel = {
                showUpdateDialog = false
//                updateManager.resetStates()
            })
    }

    // 下载进度弹框
    UpdateProgressDialog(visible = showProgressDialog, downloadState = downloadState, onCancel = {
        updateManager.cancelDownload()
        // 不立即关闭弹框，等待用户在取消状态下点击确定
        LogX.d("用户取消下载")
    }, onRetry = {
        currentVersionInfo?.let { versionInfo ->
            updateManager.startDownload(versionInfo)
        }
    }, onDismiss = {
        showProgressDialog = false
        isCheckingVersion = false
        LogX.d("关闭进度弹框，重置状态")
    })

    LoadingDialog(visible = isCheckingVersion)

    NavHost(
        navController = navController,
        startDestination = Screen.Settings.route,
    ) {
        composable(Screen.Settings.route) {
            SettingsScreenContent(
                uiState = uiState,
                updateState = updateState,
                onFloatingWindowToggle = { enabled ->
                    viewModel.toggleFloatingWindow(enabled)
                },
                onGlobalNotificationToggle = { enabled ->
                    viewModel.toggleGlobalNotification(enabled)
                },
                onLanguageClick = {
                    navController.navigate(Screen.Language.route)
                },
                onBlacklistClick = {
                    navController.navigate(Screen.Blacklist.route)
                },
                onPrivacyPolicyClick = {
                    navController.navigate(
                        Screen.WebView.createRoute(
                            context.getString(R.string.privacy_policy),
                            Constants.Agreement.PRIVACY_URL
                        )
                    )
                },
                onUserAgreementClick = {
                    navController?.navigate(
                        Screen.WebView.createRoute(
                            context.getString(R.string.user_agreement),
                            Constants.Agreement.REGISTRATION_URL
                        )
                    )
                },
                onAboutUsClick = {
                    navController?.navigate(Screen.About.route)
                },
                onClearCacheClick = {
                    viewModel.clearCache(context)
                },
                onVersionClick = {
                    isCheckingVersion = true
                    isUserTriggeredCheck = true
                    // 直接调用版本检查接口
                    updateManager.checkUpdate(true)
                },
                onLogoutClick = { callback ->
                    viewModel.logout(context) {
                        callback()
                    }
                })
        }
        // 关于我们页面
        composable(Screen.About.route) {
            AboutScreen(
                onNavigateBack = {
                    navController.popBackStack()
                })
        }

        // 黑名单页面
        composable(Screen.Blacklist.route) {
            BlacklistScreen(
                onNavigateBack = {
                    navController.popBackStack()
                })
        }

        // 语言选择页面
        composable(Screen.Language.route) {
            LanguageScreen(onNavigateBack = {
                navController.popBackStack()
            })
        }

        // WebView页面
        composable(
            route = Screen.WebView.route,
            arguments = listOf(
                navArgument("title") { type = NavType.StringType },
                navArgument("url") { type = NavType.StringType })
        ) { backStackEntry ->
            val encodedTitle = backStackEntry.arguments?.getString("title") ?: ""
            val encodedUrl = backStackEntry.arguments?.getString("url") ?: ""

            val title = runCatching {
                java.net.URLDecoder.decode(encodedTitle, "UTF-8")
            }.getOrElse { encodedTitle }

            val url = runCatching {
                java.net.URLDecoder.decode(encodedUrl, "UTF-8")
            }.getOrElse {
                encodedUrl
            }

            WebViewScreen(
                title = title, url = url, onNavigateBack = {
                    navController.popBackStack()
                })
        }
    }
}

@Composable
fun SettingsScreenContent(
    uiState: SettingsUiState,
    updateState: UpdateState? = null,
    onFloatingWindowToggle: (Boolean) -> Unit = {},
    onGlobalNotificationToggle: (Boolean) -> Unit = {},
    onLanguageClick: () -> Unit = {},
    onBlacklistClick: () -> Unit = {},
    onPrivacyPolicyClick: () -> Unit = {},
    onUserAgreementClick: () -> Unit = {},
    onAboutUsClick: () -> Unit = {},
    onClearCacheClick: () -> Unit = {},
    onVersionClick: () -> Unit = {},
    onLogoutClick: (callback: () -> Unit) -> Unit = {}
) {
    var showLogoutDialog by remember { mutableStateOf(false) }
    var showClearCacheDialog by remember { mutableStateOf(false) }
    AnchorScaffold(topBar = {
        AnchorTopBar(
            stringResource(R.string.settings)
        )
    }) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.spacedBy(1.dp)
        ) {
            // 悬浮窗开关
//            SettingsItemWithSwitch(
//                title = "Floating Window",
//                subtitle = "Show when app is in background",
//                checked = uiState.isFloatingWindowEnabled,
//                onCheckedChange = onFloatingWindowToggle
//            )

            // 全局通知开关
            SettingsItemWithSwitch(
                title = stringResource(R.string.notification),
                checked = uiState.isGlobalNotificationEnabled,
                onCheckedChange = onGlobalNotificationToggle
            )

            // Language
            SettingsItem(
                title = stringResource(R.string.language), onClick = onLanguageClick
            )

            // Blacklist
            SettingsItem(
                title = stringResource(R.string.blacklist), onClick = onBlacklistClick
            )

            // Privacy policy
            SettingsItem(
                title = stringResource(R.string.privacy_policy), onClick = onPrivacyPolicyClick
            )

            // User agreement
            SettingsItem(
                title = stringResource(R.string.user_agreement), onClick = onUserAgreementClick
            )

            // About us
            SettingsItem(
                title = stringResource(R.string.about_us), onClick = onAboutUsClick
            )

            // Clear Cache
            SettingsItem(
                title = stringResource(R.string.clear_cache),
                subtitle = if (uiState.isLoading) stringResource(R.string.clearing) else uiState.cacheSize,
                onClick = if (uiState.isLoading) {
                    {}
                } else {
                    { showClearCacheDialog = true }
                })

            // Version
            SettingsItem(
                title = stringResource(R.string.version),
                subtitle = "v${BuildConfig.VERSION_NAME}",
                hasRedDot = updateState is UpdateState.UpdateAvailable,
                onClick = onVersionClick
            )

            // 底部间距
            Spacer(modifier = Modifier.weight(1f))

            // 退出登录按钮
            AnchorDangerButton(
                stringResource(R.string.log_out),
                onClick = { showLogoutDialog = true },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp, vertical = 32.dp)
            )
        }

        // 退出登录确认弹框
        ConfirmDialog(
            visible = showLogoutDialog,
            content = stringResource(R.string.are_you_sure_you_want_to_logout),
            confirmText = stringResource(R.string.confirm),
            cancelText = stringResource(R.string.cancel),
            onDismiss = { showLogoutDialog = false },
            onConfirm = {
                showLogoutDialog = false
                onLogoutClick {
                    // 退出登录完成后的回调
                }
            },
            onCancel = { showLogoutDialog = false })

        // 清理缓存确认弹框
        ConfirmDialog(
            visible = showClearCacheDialog,
            content = stringResource(R.string.clear_all_cache_tips),
            confirmText = stringResource(R.string.clear),
            cancelText = stringResource(R.string.cancel),
            onDismiss = { showClearCacheDialog = false },
            onConfirm = {
                showClearCacheDialog = false
                onClearCacheClick()
            },
            onCancel = { showClearCacheDialog = false })
    }
}

/**
 * 设置项组件
 */
@Composable
private fun SettingsItem(
    title: String, subtitle: String? = null, hasRedDot: Boolean = false, onClick: () -> Unit
) {
    Row(modifier = Modifier
        .fillMaxWidth()
        .clickable { onClick() }
        .padding(horizontal = 24.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically) {
        Row(
            modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
                modifier = Modifier.weight(1F)
            )

            subtitle?.let {
                Text(
                    text = it,
                    color = Color(0xFF9E9E9E),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                )
            }

            if (hasRedDot) {
                Box(
                    modifier = Modifier
                        .padding(horizontal = 4.dp)
                        .width(8.dp)
                        .height(8.dp)
                        .background(Color.Red, shape = CircleShape)
                )
            }
        }

        Icon(
            imageVector = Icons.Default.KeyboardArrowRight,
            contentDescription = null,
            tint = Color(0xFF9E9E9E)
        )
    }
}

/**
 * 带开关的设置项组件
 */
@Composable
private fun SettingsItemWithSwitch(
    title: String, subtitle: String? = null, checked: Boolean, onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title, color = Color.White, fontSize = 14.sp, fontWeight = FontWeight.Normal
            )

            subtitle?.let {
                Text(
                    text = it,
                    color = Color(0xFF9E9E9E),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }

        Switch(
            checked = checked, onCheckedChange = onCheckedChange, colors = SwitchDefaults.colors(
                checkedThumbColor = Color(0xFF9F2AF8),
                checkedTrackColor = Color(0xFF9F2AF8).copy(alpha = 0.5f),
                uncheckedThumbColor = Color.Gray,
                uncheckedTrackColor = Color.Gray.copy(alpha = 0.5f)
            )
        )
    }
}

@Preview
@Composable
fun SettingsScreenPreview() {
    AnchorTheme {
        SettingsScreenContent(SettingsUiState())
    }
}
