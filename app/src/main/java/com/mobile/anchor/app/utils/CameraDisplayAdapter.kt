package com.mobile.anchor.app.utils

import android.content.Context
import android.graphics.Matrix
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.util.Size
import android.view.Surface
import android.view.TextureView
import android.view.WindowManager
import androidx.core.content.ContextCompat
import kotlin.math.abs
import kotlin.math.max

/**
 * 相机显示适配器 - 处理不同设备的相机全屏显示适配
 */
object CameraDisplayAdapter {

    /**
     * 设备特定的配置
     */
    private data class DeviceConfig(
        val rotationOffset: Int = 0,
        val needsExtraScale: Boolean = false,
        val scaleMultiplier: Float = 1.0f
    )

    /**
     * 获取设备特定配置
     */
    private fun getDeviceConfig(): DeviceConfig {
        val manufacturer = android.os.Build.MANUFACTURER.lowercase()
        val model = android.os.Build.MODEL.lowercase()
        
        return when {
            manufacturer.contains("samsung") -> DeviceConfig(
                rotationOffset = -90,
                needsExtraScale = false, // 减少额外缩放
                scaleMultiplier = 1.0f
            )
            manufacturer.contains("huawei") -> DeviceConfig(
                rotationOffset = -90,
                needsExtraScale = false,
                scaleMultiplier = 1.0f
            )
            manufacturer.contains("xiaomi") -> DeviceConfig(
                rotationOffset = -90,
                needsExtraScale = false,
                scaleMultiplier = 1.0f
            )
            manufacturer.contains("oppo") || manufacturer.contains("oneplus") -> DeviceConfig(
                rotationOffset = -90,
                needsExtraScale = false,
                scaleMultiplier = 1.0f
            )
            manufacturer.contains("vivo") -> DeviceConfig(
                rotationOffset = -90,
                needsExtraScale = false,
                scaleMultiplier = 1.0f
            )
            else -> DeviceConfig(
                rotationOffset = -90,
                needsExtraScale = false,
                scaleMultiplier = 1.0f
            )
        }
    }

    /**
     * 简化的全屏变换矩阵计算 - 解决过度缩放和右侧黑边问题
     */
    fun calculateSimpleFullScreenMatrix(
        textureView: TextureView,
        previewSize: Size,
        isFrontCamera: Boolean = true
    ): Matrix {
        val viewWidth = textureView.width
        val viewHeight = textureView.height

        if (viewWidth == 0 || viewHeight == 0 || previewSize.width == 0 || previewSize.height == 0) {
            return Matrix()
        }

        val matrix = Matrix()
        val centerX = viewWidth / 2f
        val centerY = viewHeight / 2f

        // 1. 先应用90度旋转（相机传感器通常是横向的）
        matrix.postRotate(90f, centerX, centerY)

        // 2. 计算旋转后的适配缩放
        // 旋转90度后，预览的宽高需要交换
        val rotatedPreviewWidth = previewSize.height.toFloat()
        val rotatedPreviewHeight = previewSize.width.toFloat()

        val scaleX = viewWidth.toFloat() / rotatedPreviewWidth
        val scaleY = viewHeight.toFloat() / rotatedPreviewHeight

        // 3. 使用适中的缩放策略 - 既要填满屏幕又不能过度放大
        val scale = when {
            scaleX > scaleY -> {
                // 如果X方向缩放更大，说明预览比较窄，使用Y方向缩放并稍微放大
                scaleY * 1.05f
            }
            else -> {
                // 如果Y方向缩放更大，说明预览比较矮，使用X方向缩放并稍微放大
                scaleX * 1.05f
            }
        }

        // 4. 应用缩放
        matrix.postScale(scale, scale, centerX, centerY)

        // 5. 前置摄像头镜像
        if (isFrontCamera) {
            matrix.postScale(-1f, 1f, centerX, centerY)
        }

        return matrix
    }

    /**
     * 计算最佳的变换矩阵以实现全屏显示
     */
    fun calculateTransformMatrix(
        textureView: TextureView,
        previewSize: Size,
        isFrontCamera: Boolean = true
    ): Matrix {
        val viewWidth = textureView.width
        val viewHeight = textureView.height
        
        if (viewWidth == 0 || viewHeight == 0 || previewSize.width == 0 || previewSize.height == 0) {
            return Matrix()
        }

        val matrix = Matrix()
        val centerX = viewWidth / 2f
        val centerY = viewHeight / 2f
        val deviceConfig = getDeviceConfig()

        // 1. 获取显示旋转信息
        val windowManager = ContextCompat.getSystemService(textureView.context, WindowManager::class.java)
        val displayRotation = windowManager?.defaultDisplay?.rotation ?: Surface.ROTATION_0
        val displayRotationDegrees = getSurfaceOrientationDegrees(displayRotation)

        // 2. 计算传感器方向
        val sensorOrientation = getSensorOrientation(textureView.context, isFrontCamera)
        
        // 3. 计算旋转补偿
        val rotationCompensation = (sensorOrientation - displayRotationDegrees + 360) % 360
        
        // 4. 应用设备特定的旋转调整
        val finalRotation = (rotationCompensation + deviceConfig.rotationOffset + 360) % 360

        // 5. 应用旋转
        matrix.postRotate(finalRotation.toFloat(), centerX, centerY)

        // 6. 应用镜像（仅前置摄像头）
        if (isFrontCamera) {
            when (rotationCompensation) {
                90, 270 -> matrix.postScale(1f, -1f, centerX, centerY)
                else -> matrix.postScale(-1f, 1f, centerX, centerY)
            }
        }

        // 7. 计算缩放 - 修复过度缩放问题
        val (rotatedWidth, rotatedHeight) = if (rotationCompensation == 90 || rotationCompensation == 270) {
            Pair(previewSize.height.toFloat(), previewSize.width.toFloat())
        } else {
            Pair(previewSize.width.toFloat(), previewSize.height.toFloat())
        }

        val scaleX = viewWidth / rotatedWidth
        val scaleY = viewHeight / rotatedHeight

        // 使用更智能的缩放策略
        val baseScale = calculateSmartScale(scaleX, scaleY, viewWidth, viewHeight, rotatedWidth, rotatedHeight)

        // 应用设备特定的缩放调整（减少额外缩放）
        val finalScale = if (deviceConfig.needsExtraScale) {
            baseScale * kotlin.math.min(deviceConfig.scaleMultiplier, 1.05f) // 限制最大额外缩放
        } else {
            baseScale
        }

        matrix.postScale(finalScale, finalScale, centerX, centerY)

        // 8. 计算平移以居中
        val scaledWidth = rotatedWidth * finalScale
        val scaledHeight = rotatedHeight * finalScale
        val dx = (viewWidth - scaledWidth) / 2f
        val dy = (viewHeight - scaledHeight) / 2f
        matrix.postTranslate(dx, dy)

        return matrix
    }

    /**
     * 智能缩放计算 - 避免过度缩放，实现更好的全屏效果
     */
    private fun calculateSmartScale(
        scaleX: Float,
        scaleY: Float,
        viewWidth: Int,
        viewHeight: Int,
        previewWidth: Float,
        previewHeight: Float
    ): Float {
        val viewRatio = viewWidth.toFloat() / viewHeight.toFloat()
        val previewRatio = previewWidth / previewHeight

        return when {
            // 如果预览比例接近视图比例，使用较小的缩放以避免过度放大
            kotlin.math.abs(viewRatio - previewRatio) < 0.1f -> {
                kotlin.math.min(scaleX, scaleY) * 1.02f // 稍微放大以消除可能的黑边
            }
            // 如果预览更宽，优先适配高度
            previewRatio > viewRatio -> {
                scaleY * 0.98f // 稍微缩小以确保不会过度放大
            }
            // 如果预览更高，优先适配宽度
            else -> {
                scaleX * 0.98f
            }
        }
    }

    /**
     * 获取传感器方向
     */
    private fun getSensorOrientation(context: Context, isFrontCamera: Boolean): Int {
        val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        var sensorOrientation = 0

        try {
            for (cameraId in cameraManager.cameraIdList) {
                val characteristics = cameraManager.getCameraCharacteristics(cameraId)
                val cameraFacing = characteristics.get(CameraCharacteristics.LENS_FACING)

                if (isFrontCamera && cameraFacing == CameraCharacteristics.LENS_FACING_FRONT) {
                    sensorOrientation = characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION) ?: 0
                    break
                } else if (!isFrontCamera && cameraFacing == CameraCharacteristics.LENS_FACING_BACK) {
                    sensorOrientation = characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION) ?: 0
                    break
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return sensorOrientation
    }

    /**
     * 获取表面方向角度
     */
    private fun getSurfaceOrientationDegrees(rotation: Int): Int {
        return when (rotation) {
            Surface.ROTATION_0 -> 0
            Surface.ROTATION_90 -> 90
            Surface.ROTATION_180 -> 180
            Surface.ROTATION_270 -> 270
            else -> 0
        }
    }

    /**
     * 检查是否需要特殊处理的设备
     */
    fun isProblematicDevice(): Boolean {
        val manufacturer = android.os.Build.MANUFACTURER.lowercase()
        val model = android.os.Build.MODEL.lowercase()
        
        // 可以根据实际测试结果添加更多设备
        val problematicDevices = listOf(
            "samsung",
            "huawei", 
            "honor",
            "xiaomi",
            "redmi",
            "oppo",
            "oneplus",
            "vivo",
            "realme"
        )
        
        return problematicDevices.any { manufacturer.contains(it) }
    }

    /**
     * 获取推荐的预览尺寸配置
     */
    fun getRecommendedPreviewConfig(displayWidth: Int, displayHeight: Int): PreviewConfig {
        val deviceConfig = getDeviceConfig()
        val aspectRatio = displayWidth.toFloat() / displayHeight.toFloat()
        
        return PreviewConfig(
            preferHighResolution = !deviceConfig.needsExtraScale,
            maxWidth = if (deviceConfig.needsExtraScale) 1280 else 1920,
            aspectRatioTolerance = if (deviceConfig.needsExtraScale) 0.15f else 0.1f,
            preferredAspectRatio = aspectRatio
        )
    }

    /**
     * 预览配置数据类
     */
    data class PreviewConfig(
        val preferHighResolution: Boolean,
        val maxWidth: Int,
        val aspectRatioTolerance: Float,
        val preferredAspectRatio: Float
    )
}
