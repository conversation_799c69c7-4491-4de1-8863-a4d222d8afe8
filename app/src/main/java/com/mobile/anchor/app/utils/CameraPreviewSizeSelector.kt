package com.mobile.anchor.app.utils

import android.util.Size
import kotlin.math.abs

object CameraPreviewSizeSelector {

    /**
     * 选择与目标宽高比最接近的预览尺寸
     */
    fun closestAspectRatioSize(
        availableSizes: Array<Size>, surfaceWidth: Int, surfaceHeight: Int
    ): Size? {
        val targetWidth = surfaceHeight
        val targetHeight = surfaceWidth

        // 尝试完全匹配尺寸
        availableSizes.firstOrNull { it.width == targetWidth && it.height == targetHeight }?.let {
            return it
        }

        val targetRatio = targetWidth.toFloat() / targetHeight
        return availableSizes.minByOrNull {
            val ratio = it.width.toFloat() / it.height
            abs(ratio - targetRatio)
        }
    }

    /**
     * 选择合适的预览尺寸，确保比例匹配且宽度不超过最大限制
     */
    fun optimizedPreviewSize(
        availableSizes: Array<Size>, surfaceWidth: Int, surfaceHeight: Int, maxWidth: Int = 1000
    ): Size? {
        val targetRatio = surfaceWidth.toFloat() / surfaceHeight

        val matchingSizes = availableSizes.filter {
            val ratio = it.height.toFloat() / it.width
            ratio == targetRatio
        }

        val selected = matchingSizes.filter { it.width >= maxWidth }.minByOrNull { it.width }

        return selected ?: matchingSizes.lastOrNull() ?: closestAspectRatioSize(
            availableSizes,
            surfaceWidth,
            surfaceHeight
        )
    }
}
