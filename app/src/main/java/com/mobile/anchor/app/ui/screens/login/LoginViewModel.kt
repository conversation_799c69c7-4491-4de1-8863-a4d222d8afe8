package com.mobile.anchor.app.ui.screens.login

import android.util.Patterns
import androidx.lifecycle.viewModelScope
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.mobile.anchor.app.data.model.AuthBean
import com.mobile.anchor.app.data.model.EmailVerificationUiState
import com.mobile.anchor.app.data.model.LoginUiState
import com.mobile.anchor.app.data.network.ApiResult
import com.mobile.anchor.app.data.network.ktnet.NetDelegates
import com.mobile.anchor.app.data.service.BodyParams
import com.mobile.anchor.app.data.service.UserApiService
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.viewmodels.BaseViewModel
import com.mobile.anchor.app.utils.RongYunUtil
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 登录ViewModel
 */
class LoginViewModel() : BaseViewModel() {

    private val userApiService: UserApiService by NetDelegates()

    // 登录页面状态
    private val _loginUiState = MutableStateFlow(LoginUiState())
    val loginUiState: StateFlow<LoginUiState> = _loginUiState.asStateFlow()

    // 邮箱验证页面状态
    private val _emailVerificationUiState = MutableStateFlow(EmailVerificationUiState())
    val emailVerificationUiState: StateFlow<EmailVerificationUiState> =
        _emailVerificationUiState.asStateFlow()

    // 倒计时任务
    private var countDownJob: Job? = null

    /**
     * 更新邮箱输入
     */
    fun updateEmail(email: String) {
        val isValid = Patterns.EMAIL_ADDRESS.matcher(email.trim()).matches()
        _loginUiState.value = _loginUiState.value.copy(
            email = email, isEmailValid = isValid, errorMessage = null
        )
    }

    /**
     * 获取邮箱验证码
     */
    fun getEmailCode() {
        val email = _loginUiState.value.email.trim()

        if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
            _loginUiState.value = _loginUiState.value.copy(
                errorMessage = "Email format incorrect"
            )
            return
        }

        _loginUiState.value = _loginUiState.value.copy(isLoading = true, errorMessage = null)
        LogX.d("开始获取邮箱验证码: $email")

        ktHttpRequest {
            val response = userApiService.getEmailCode(
                BodyParams.codeBody(
                    email = email
                )
            ).await()
            response?.let { data ->
                LogX.d("获取邮箱验证码成功")
                _loginUiState.value = _loginUiState.value.copy(
                    isLoading = false, isCodeSent = true
                )
                // 切换到验证码页面状态，确保邮箱正确设置
                _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
                    email = email,
                    timeRemaining = 300,
                    canResendCode = false,
                    errorMessage = null,
                    verificationCode = "" // 清空之前的验证码
                )
                startCountdown()
            }
        }
    }

    /**
     * 设置邮箱到验证状态
     */
    fun setEmailForVerification(email: String) {
        LogX.d("设置邮箱到验证状态: $email")
        val currentState = _emailVerificationUiState.value

        // 只有当邮箱不同时才更新状态
        if (currentState.email != email) {
            _emailVerificationUiState.value = currentState.copy(
                email = email, errorMessage = null
            )
        }

        // 如果验证码已发送且倒计时还在进行中，不需要重新启动倒计时
        // 只有在倒计时未启动或已结束时才启动
        if (countDownJob == null && currentState.timeRemaining > 0 && !currentState.canResendCode) {
            LogX.d("启动倒计时，剩余时间: ${currentState.timeRemaining}")
            startCountdown()
        }
    }

    /**
     * 更新验证码输入
     */
    fun updateVerificationCode(code: String) {
        _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
            verificationCode = code, errorMessage = null
        )
    }

    /**
     * 使用验证码登录
     */
    fun loginWithCode() {
        val state = _emailVerificationUiState.value
        val email = state.email
        val code = state.verificationCode

        if (code.length != 6) {
            _emailVerificationUiState.value = state.copy(
                errorMessage = "Please enter 6-digit code"
            )
            return
        }

        _emailVerificationUiState.value = state.copy(isLoading = true, errorMessage = null)

        ktHttpRequest {
            val response = userApiService.userLogin(
                BodyParams.loginBody(
                    auth_token = code,
                    auth_id = email.trim(),
                    auth_type = 2,
                    gender = 2,
                    country_id = 0,
                )
            ).await()
            response?.let { data ->
                handleLoginSuccess(data)
            }
        }
    }

    /**
     * 重新发送验证码
     */
    fun resendCode() {
        if (!_emailVerificationUiState.value.canResendCode) {
            return
        }

        val email = _emailVerificationUiState.value.email
        _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
            isLoading = true, errorMessage = null
        )

        // 直接发送验证码，不调用 getEmailCode() 避免设置 isCodeSent = true
        ktHttpRequest {
            val response = userApiService.getEmailCode(
                BodyParams.codeBody(
                    email = email.trim()
                )
            ).await()
            response?.let { data ->
                _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
                    isLoading = false,
                    timeRemaining = 300,
                    canResendCode = false,
                    errorMessage = null
                )
                startCountdown()
                LogX.d("重新发送验证码成功")
            }
        }
    }

    /**
     * 开始倒计时
     */
    private fun startCountdown() {
        // 取消之前的倒计时
        countDownJob?.cancel()
        countDownJob = viewModelScope.launch {
            var currentTime = _emailVerificationUiState.value.timeRemaining
            while (currentTime > 0) {
                delay(1000)
                currentTime -= 1
                _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
                    timeRemaining = currentTime, canResendCode = currentTime <= 0
                )
            }
        }
    }

    /**
     * Google登录
     */
    fun loginWithGoogle(googleId: String, googleEmail: String, googleToken: String) {
        _loginUiState.value = _loginUiState.value.copy(
            isGoogleLoading = true, errorMessage = null
        )

        ktHttpRequest {
            if (DataStoreManager.isUserLoggedIn()) {
                val response =
                    userApiService.bindGoogle(BodyParams.bindGoogleBody(googleId, googleToken))
                        .await()
                response?.let { data ->
                    _loginUiState.value = _loginUiState.value.copy(
                        isGoogleLoading = false, errorMessage = null, googleLoginSuccess = true
                    )
                }
            } else {
                val response = userApiService.userLogin(
                    BodyParams.loginBody(
                        auth_token = googleToken,
                        auth_id = googleEmail,
                        auth_type = 1,
                        gender = 2,
                        country_id = 7,
                    )
                ).await()
                response?.let { data ->
                    handleLoginSuccess(data, true)
                }
            }
        }
    }

    /**
     * 处理登录成功
     */
    private suspend fun handleLoginSuccess(authBean: AuthBean, isGoogleLogin: Boolean = false) {
        try {
            // 使用统一的登录数据保存方法
            DataStoreManager.saveLoginData(authBean)

            // 保存用户信息
            val user = authBean.anchor
            if (user != null) {
                // 初始化融云IM
                RongYunUtil.initRongYunIM(user.rongcloudAppID)

                if (isGoogleLogin) {
                    _loginUiState.value = _loginUiState.value.copy(
                        isGoogleLoading = false, googleLoginSuccess = true
                    )
                } else {
                    _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
                        isLoading = false, isLoginSuccessful = true
                    )
                }

                LogX.d("登录成功，用户ID: ${user.id}, 注册状态: stat=${user.stat} isRegistration: ${user.isRegistration}, isRegistrationCompleted: ${user.isVerified}")
            } else {
                LogX.e("用户信息为空")
                _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
                    isLoading = false, errorMessage = "User information acquisition failed"
                )
            }

        } catch (e: Exception) {
            LogX.e("保存登录信息失败", e)
            _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
                isLoading = false, errorMessage = "Login information save failed"
            )
        }
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _loginUiState.value = _loginUiState.value.copy(errorMessage = null)
        _emailVerificationUiState.value = _emailVerificationUiState.value.copy(errorMessage = null)
    }

    /**
     * 重置验证码发送状态
     * 用于从验证码页面返回时重置状态，避免自动跳转
     */
    fun resetCodeSentState() {
        _loginUiState.value = _loginUiState.value.copy(isCodeSent = false)
        // 停止倒计时
        countDownJob?.cancel()
        countDownJob = null
        // 重置验证码页面状态
        _emailVerificationUiState.value = _emailVerificationUiState.value.copy(
            verificationCode = "", timeRemaining = 300, canResendCode = false, errorMessage = null
        )
        LogX.d("重置验证码发送状态和倒计时")
    }

    /**
     * 格式化倒计时时间
     */
    fun formatTime(seconds: Int): String {
        if (seconds <= 0) return "00:00"
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        return String.format("%02d:%02d", minutes, remainingSeconds)
    }

    /**
     * 处理Google登录结果
     */
    fun handleGoogleSignInResult(result: ApiResult<GoogleSignInAccount>) {
        when (result) {
            is ApiResult.Success -> {
                val account = result.data
                val email = account.email ?: ""
                val idToken = account.idToken ?: ""

                if (email.isNotEmpty() && idToken.isNotEmpty()) {
                    LogX.d("Google登录账户信息获取成功: $email")
                    loginWithGoogle(account.id ?: "", email, idToken)
                } else {
                    _loginUiState.value = _loginUiState.value.copy(
                        isGoogleLoading = false,
                        errorMessage = "Google account information retrieval failed"
                    )
                    LogX.e("Google账户信息不完整: email=$email, idToken=${idToken.isNotEmpty()}")
                }
            }

            is ApiResult.Error -> {
                val errorMessage = result.exception?.message ?: "Google login failed"
                _loginUiState.value = _loginUiState.value.copy(
                    isGoogleLoading = false,
                    errorMessage = if (errorMessage.contains("cancel login")) null else errorMessage
                )
                LogX.e("Google登录错误: $errorMessage")
            }

            is ApiResult.Loading -> {
                // 处理加载状态（如果需要）
                _loginUiState.value = _loginUiState.value.copy(
                    isGoogleLoading = true, errorMessage = null
                )
            }
        }
    }

    /**
     * 重置登录状态
     */
    fun resetLoginState() {
        LogX.d("重置登录状态")
        _loginUiState.value = LoginUiState()
        _emailVerificationUiState.value = EmailVerificationUiState()

        // 停止倒计时
        countDownJob?.cancel()
        countDownJob = null

        LogX.d("登录状态重置完成")
    }
}
