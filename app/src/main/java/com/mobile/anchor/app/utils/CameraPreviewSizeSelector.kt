package com.mobile.anchor.app.utils

import android.util.Size
import kotlin.math.abs
import kotlin.math.min

object CameraPreviewSizeSelector {

    /**
     * 选择与目标宽高比最接近的预览尺寸
     */
    fun closestAspectRatioSize(
        availableSizes: Array<Size>, surfaceWidth: Int, surfaceHeight: Int
    ): Size? {
        if (availableSizes.isEmpty()) return null

        val targetRatio = surfaceWidth.toFloat() / surfaceHeight

        // 尝试完全匹配尺寸
        availableSizes.firstOrNull { it.width == surfaceWidth && it.height == surfaceHeight }?.let {
            return it
        }

        // 按照宽高比差异排序，选择最接近的
        return availableSizes.minByOrNull {
            val ratio = it.width.toFloat() / it.height
            abs(ratio - targetRatio)
        }
    }

    /**
     * 优化的预览尺寸选择算法，更好地适配全屏显示
     */
    fun optimizedPreviewSize(
        availableSizes: Array<Size>,
        surfaceWidth: Int,
        surfaceHeight: Int,
        maxWidth: Int = 1920
    ): Size? {
        if (availableSizes.isEmpty()) return Size(640, 480)

        val targetRatio = surfaceWidth.toFloat() / surfaceHeight
        val tolerance = 0.1f // 宽高比容差

        // 1. 首先尝试找到完全匹配的尺寸
        availableSizes.firstOrNull { it.width == surfaceWidth && it.height == surfaceHeight }?.let {
            return it
        }

        // 2. 筛选出宽高比接近的尺寸
        val matchingSizes = availableSizes.filter { size ->
            val ratio = size.width.toFloat() / size.height
            abs(ratio - targetRatio) <= tolerance
        }.sortedByDescending { it.width * it.height } // 按分辨率降序排列

        if (matchingSizes.isNotEmpty()) {
            // 3. 在匹配的尺寸中选择合适的分辨率
            // 优先选择高质量但不超过最大宽度限制的尺寸
            val highQualitySize = matchingSizes.firstOrNull { it.width <= maxWidth }
            if (highQualitySize != null) {
                return highQualitySize
            }

            // 如果都超过最大宽度，选择最小的那个
            return matchingSizes.lastOrNull()
        }

        // 4. 如果没有找到宽高比匹配的，选择最接近的
        return closestAspectRatioSize(availableSizes, surfaceWidth, surfaceHeight)
    }

    /**
     * 为全屏显示选择最佳预览尺寸 - 结合设备适配
     */
    fun bestFullScreenSize(
        availableSizes: Array<Size>,
        displayWidth: Int,
        displayHeight: Int
    ): Size? {
        if (availableSizes.isEmpty()) return Size(640, 480)

        // 获取设备特定的配置
        val previewConfig = CameraDisplayAdapter.getRecommendedPreviewConfig(displayWidth, displayHeight)
        val displayRatio = previewConfig.preferredAspectRatio

        // 按照以下优先级选择：
        // 1. 宽高比匹配且分辨率适中的尺寸
        // 2. 常见的高质量尺寸（根据设备配置调整）
        // 3. 最接近显示比例的尺寸

        val preferredSizes = if (previewConfig.preferHighResolution) {
            listOf(
                Size(1920, 1080), // 1080p
                Size(1280, 720),  // 720p
                Size(1440, 1080), // 4:3 高清
                Size(960, 720),   // 4:3 标清
                Size(640, 480)    // VGA
            )
        } else {
            listOf(
                Size(1280, 720),  // 720p
                Size(960, 720),   // 4:3 标清
                Size(1920, 1080), // 1080p
                Size(640, 480)    // VGA
            )
        }

        // 首先尝试找到支持的首选尺寸
        for (preferred in preferredSizes) {
            if (preferred.width > previewConfig.maxWidth) continue

            availableSizes.firstOrNull { it.width == preferred.width && it.height == preferred.height }?.let {
                val ratio = it.width.toFloat() / it.height
                if (abs(ratio - displayRatio) <= previewConfig.aspectRatioTolerance) {
                    return it
                }
            }
        }

        // 如果没有找到首选尺寸，使用优化算法
        return optimizedPreviewSize(availableSizes, displayWidth, displayHeight, previewConfig.maxWidth)
    }
}
