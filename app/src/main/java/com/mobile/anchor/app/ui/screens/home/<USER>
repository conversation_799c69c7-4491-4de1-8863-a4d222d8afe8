package com.mobile.anchor.app.ui.screens.home

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.graphics.Matrix
import android.graphics.SurfaceTexture
import android.hardware.camera2.CameraCaptureSession
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraDevice
import android.hardware.camera2.CameraManager
import android.os.Bundle
import android.util.Size
import android.view.Surface
import android.view.TextureView
import android.view.WindowManager
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import com.bdc.android.library.extension.jump
import com.bdc.android.library.utils.ToastUtil
import com.mobile.anchor.app.R
import com.mobile.anchor.app.data.model.DashboardBean
import com.mobile.anchor.app.extension.formatAsPercent
import com.mobile.anchor.app.extension.formatCountdown
import com.mobile.anchor.app.extension.toMinutes
import com.mobile.anchor.app.extension.toShowDiamond
import com.mobile.anchor.app.lifecycle.AppLifecycleObserver
import com.mobile.anchor.app.logger.LogX
import com.mobile.anchor.app.manager.DataStoreManager
import com.mobile.anchor.app.ui.activities.LevelActivity
import com.mobile.anchor.app.ui.components.AnchorScaffold
import com.mobile.anchor.app.ui.components.AnchorTopBar
import com.mobile.anchor.app.ui.components.CardView
import com.mobile.anchor.app.ui.components.ConfirmDialog
import com.mobile.anchor.app.ui.components.LoadingDialog
import com.mobile.anchor.app.ui.conversation.MyRongConversationActivity
import com.mobile.anchor.app.ui.theme.AnchorTheme
import com.mobile.anchor.app.ui.theme.Primary
import com.mobile.anchor.app.ui.view.MarqueeView
import com.mobile.anchor.app.utils.CameraPreviewSizeSelector
import com.mobile.anchor.app.utils.CameraDisplayAdapter
import com.mobile.anchor.app.utils.Constants
import io.rong.imlib.model.Conversation

/**
 * 首页界面
 */
@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterialApi::class)
@Composable
fun HomeScreen() {
    val context = LocalContext.current
    val viewModel: HomeViewModel = viewModel()
    val lifecycleOwner = LocalLifecycleOwner.current

    val uiState by viewModel.uiState.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    var showLoadingDialog by remember { mutableStateOf(false) }

    // 摄像头相关状态
    val cameraManager: CameraManager? =
        remember { context.getSystemService(Context.CAMERA_SERVICE) as? CameraManager }
    var cameraDevice by remember { mutableStateOf<CameraDevice?>(null) }
    var captureSession by remember { mutableStateOf<CameraCaptureSession?>(null) }
    var textureView by remember { mutableStateOf<TextureView?>(null) }
    var cameraId by remember { mutableStateOf("") }
    var previewSize by remember { mutableStateOf<Size?>(null) }
    var showCameraPreview by remember { mutableStateOf(false) }

    // 权限请求
    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted && uiState.workbenchBean.working_mod) {
            showCameraPreview = true
            startCameraPreview(
                context, cameraManager, textureView, cameraId, previewSize
            ) { device, session ->
                cameraDevice = device
                captureSession = session
            }
        }
    }

    // 弹窗确认状态
    var showConfirmDialog by remember { mutableStateOf(false) }
    var pendingWorkModeStatus by remember { mutableStateOf(false) }

    // 说明弹框状态
    var showInfoDialog by remember { mutableStateOf(false) }
    var infoDialogTitle by remember { mutableStateOf("") }
    var infoDialogContent by remember { mutableStateOf("") }

    LaunchedEffect(Unit) {
        // 检测冷启动并自动开启工作模式
        val isColdStart = AppLifecycleObserver.getInstance().checkAndMarkColdStart()
        if (isColdStart && DataStoreManager.getUserObject()?.isVerified == true) {
            viewModel.autoEnableWorkMode()
        } else {
            LogX.d("从后台恢复，不自动开启工作模式")
        }
    }

    // 监听在线状态变化
    LaunchedEffect(uiState.workbenchBean.working_mod) {
        pendingWorkModeStatus = uiState.workbenchBean.working_mod
        showLoadingDialog = false
        if (uiState.workbenchBean.working_mod) {
            if (ContextCompat.checkSelfPermission(
                    context, Manifest.permission.CAMERA
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                showCameraPreview = true
                startCameraPreview(
                    context, cameraManager, textureView, cameraId, previewSize
                ) { device, session ->
                    cameraDevice = device
                    captureSession = session
                }
            } else {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        } else {
            showCameraPreview = false
            stopCameraPreview(cameraDevice, captureSession)
        }
    }

    // 初始化摄像头
    LaunchedEffect(Unit) {
        setupCamera(context) { id, size ->
            cameraId = id
            previewSize = size
        }
    }

    //监听页面生命周期，管理摄像头预览
    DisposableEffect(lifecycleOwner) {
        val observer = androidx.lifecycle.LifecycleEventObserver { _, event ->
            when (event) {
                // 页面恢复时，如果在线状态为开启，则启动摄像头预览
                Lifecycle.Event.ON_RESUME -> {
                    if (uiState.workbenchBean.working_mod) {
                        if (ContextCompat.checkSelfPermission(
                                context, Manifest.permission.CAMERA
                            ) == PackageManager.PERMISSION_GRANTED
                        ) {
                            showCameraPreview = true
                            startCameraPreview(
                                context, cameraManager, textureView, cameraId, previewSize
                            ) { device, session ->
                                cameraDevice = device
                                captureSession = session
                            }
                        } else {
                            cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                        }
                    }
                }

                // 页面暂停时停止预览
                Lifecycle.Event.ON_PAUSE -> {
                    // 页面暂停时停止预览
                    showCameraPreview = false
                    stopCameraPreview(cameraDevice, captureSession)
                }

                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            // 清理摄像头资源
            stopCameraPreview(cameraDevice, captureSession)
        }
    }

    // Apply TextureView transform when previewSize or textureView changes
    LaunchedEffect(previewSize, textureView) {
        if (textureView != null && previewSize != null) {
            updateTextureViewTransform(textureView!!, previewSize!!)
        }
    }

    // 摄像头预览作为背景
    Box(modifier = Modifier.fillMaxSize()) {
        // 摄像头预览背景
        if (showCameraPreview) {
            AndroidView(
                modifier = Modifier.fillMaxSize(), factory = { ctx ->
                    TextureView(ctx).apply {
                        textureView = this

                        surfaceTextureListener = object : TextureView.SurfaceTextureListener {
                            override fun onSurfaceTextureAvailable(
                                surface: SurfaceTexture, width: Int, height: Int
                            ) {
                                // 如果预览尺寸还未确定，重新选择最佳尺寸
                                if (previewSize == null) {
                                    val availableSizes = cameraManager?.getCameraCharacteristics(cameraId)
                                        ?.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                                        ?.getOutputSizes(SurfaceTexture::class.java) ?: emptyArray()

                                    previewSize = CameraPreviewSizeSelector.bestFullScreenSize(
                                        availableSizes, width, height
                                    )
                                }

                                // 应用变换矩阵以实现全屏显示
                                previewSize?.let { size ->
                                    updateTextureViewTransform(this@apply, size)
                                }

                                // 启动相机预览
                                startCameraPreview(
                                    context, cameraManager, textureView, cameraId, previewSize
                                ) { device, session ->
                                    cameraDevice = device
                                    captureSession = session
                                }
                            }

                            override fun onSurfaceTextureSizeChanged(
                                surface: SurfaceTexture, width: Int, height: Int
                            ) {
                                // Surface尺寸改变时，重新计算最佳预览尺寸和变换
                                val availableSizes = cameraManager?.getCameraCharacteristics(cameraId)
                                    ?.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                                    ?.getOutputSizes(SurfaceTexture::class.java) ?: emptyArray()

                                val newPreviewSize = CameraPreviewSizeSelector.bestFullScreenSize(
                                    availableSizes, width, height
                                )

                                // 如果尺寸发生变化，更新预览尺寸并重新应用变换
                                if (newPreviewSize != previewSize) {
                                    previewSize = newPreviewSize
                                    // 重新启动预览以应用新尺寸
                                    stopCameraPreview(cameraDevice, captureSession)
                                    startCameraPreview(
                                        context, cameraManager, textureView, cameraId, previewSize
                                    ) { device, session ->
                                        cameraDevice = device
                                        captureSession = session
                                    }
                                } else {
                                    // 尺寸未变化，只需重新应用变换
                                    previewSize?.let { size ->
                                        updateTextureViewTransform(this@apply, size)
                                    }
                                }
                            }

                            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean =
                                true

                            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
                        }
                    }
                })
        }

        // 下拉刷新状态
        val pullRefreshState = rememberPullRefreshState(
            refreshing = isRefreshing, onRefresh = {
                viewModel.loadAllData()
            })

        // UI内容层，当摄像头预览开启时透明度为50%
        AnchorScaffold(
            modifier = Modifier.alpha(if (showCameraPreview) 0.5f else 1f), topBar = {
                AnchorTopBar(stringResource(id = R.string.home_title), showNavigationIcon = false)
            }) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = paddingValues.calculateTopPadding())
                    .pullRefresh(pullRefreshState)
            ) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(15.dp)
                ) {
                    item {
                        TaskCard(uiState.workbenchBean) { title, content ->
                            showInfoDialog = true
                            infoDialogTitle = title
                            infoDialogContent = content
                        }
                    }

                    item {
                        SwitchCard(
                            uiState = uiState, onShowConfirmDialog = { status ->
                                pendingWorkModeStatus = status
                                showConfirmDialog = true
                            })
                    }

                    // 跑马灯
                    item {
                        uiState.workbenchBean.tips_list?.let {
                            MarqueeCard(it)
                        }
                    }

                    item {
                        Workboard(
                            workbenchBean = uiState.workbenchBean,
                            onShowInfoDialog = { title, content ->
                                infoDialogTitle = title
                                infoDialogContent = content
                                showInfoDialog = true
                            },
                            onNavigateToNotification = {
                                context.jump(
                                    MyRongConversationActivity::class.java, Bundle().apply {
                                        putString("targetId", Constants.RONG_YUN_ID_SYSTEM)
                                        putString(
                                            "ConversationType",
                                            Conversation.ConversationType.SYSTEM.name
                                        )
                                    })
                            },
                            onNavigateToLevel = { context.jump(LevelActivity::class.java) })
                        Spacer(Modifier.height(15.dp))
                    }
                }

                // 下拉刷新指示器
                PullRefreshIndicator(
                    refreshing = isRefreshing,
                    state = pullRefreshState,
                    modifier = Modifier.align(Alignment.TopCenter)
                )
            }
        }
    }

    // 确认对话框
    ConfirmDialog(
        visible = showConfirmDialog,
        title = stringResource(R.string.reminder),
        content = if (pendingWorkModeStatus) stringResource(R.string.turn_on_working_tips)
        else stringResource(R.string.turn_off_working_tips),
        confirmText = stringResource(R.string.rc_confirm),
        cancelText = stringResource(R.string.rc_cancel),
        onDismiss = { showConfirmDialog = false },
        onConfirm = {
            if (DataStoreManager.getUserObject()?.isVerified == true) {
                showLoadingDialog = true
                viewModel.updateWorkModeStatus(pendingWorkModeStatus)
            } else {
                ToastUtil.show(context.getString(R.string.unable_to_activate_working_mode))
            }

            showConfirmDialog = false
        },
        onCancel = { showConfirmDialog = false })

    // 说明弹框
    ConfirmDialog(
        visible = showInfoDialog,
        title = infoDialogTitle,
        content = infoDialogContent,
        confirmText = stringResource(R.string.rc_confirm),
        showCancelButton = false, // 只显示确认按钮
        onDismiss = { showInfoDialog = false },
        onConfirm = { showInfoDialog = false })

    LoadingDialog(visible = showLoadingDialog)
}

@Composable
private fun TaskCard(dashboardBean: DashboardBean?, onShowInfoDialog: (String, String) -> Unit) {
    val context = LocalContext.current
    CardView {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.clickable {
                onShowInfoDialog(
                    "", context.getString(
                        R.string.home_instruction_explain,
                        "${dashboardBean?.each_match_effect_coin}",
                        "${dashboardBean?.match_call_effect_duration}",
                        "${dashboardBean?.anchor_cycle_times}"
                    )
                )
            }) {
                Icon(
                    painter = painterResource(R.mipmap.ic_tip),
                    contentDescription = "任务信息",
                    tint = Color.Red,
                    modifier = Modifier.size(15.dp)
                )
                Spacer(Modifier.width(4.dp))
                Text(
                    text = stringResource(
                        R.string.match_bonus_coins,
                        dashboardBean?.anchor_match_coin?.toShowDiamond() ?: 0,
                        dashboardBean?.anchor_match_times_step ?: 0,
                        dashboardBean?.anchor_cycle_times ?: 0
                    ), color = Color.Red
                )
            }

            Spacer(Modifier.height(10.dp))
            Text(
                stringResource(
                    R.string.valid_match_calls_today,
                    dashboardBean?.today_data?.anchor_effect_match_times ?: 0
                ), fontSize = 14.sp, color = Color(0xff666666)
            )
        }
    }
}

@Composable
private fun SwitchCard(
    uiState: HomeUiState, onShowConfirmDialog: (Boolean) -> Unit
) {
    val context = LocalContext.current
    CardView {
        Column(modifier = Modifier.padding(16.dp)) {
            // 在线状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(stringResource(id = R.string.work_mode), color = Color.White)
                Switch(
                    checked = uiState.workbenchBean.working_mod, onCheckedChange = { isChecked ->
                        if (DataStoreManager.getUserObject()?.isVerified == false) {
                            ToastUtil.show(context.getString(R.string.unable_to_activate_working_mode))
                            return@Switch
                        }
                        onShowConfirmDialog(isChecked)
                    }, colors = SwitchDefaults.colors(
                        checkedThumbColor = Primary, checkedTrackColor = Primary.copy(alpha = 0.5f)
                    )
                )
            }

            // 匹配开关
//            Row(
//                modifier = Modifier.fillMaxWidth(),
//                verticalAlignment = Alignment.CenterVertically,
//                horizontalArrangement = Arrangement.SpaceBetween
//            ) {
//                Text(stringResource(id = R.string.match_switch), color = Color.White)
//                Switch(
//                    checked = uiState.workbenchBean.matchStatus == "1",
//                    onCheckedChange = { isChecked ->
//                        if (uiState.workbenchBean.onlineStatus == "1") {
//                            viewModel.updateMatchSwitch(if (isChecked) "1" else "0")
//                        } else {
//                            // 可以添加Toast提示
//                        }
//                    })
//            }
        }
    }
}

/**
 * 通知卡片组件，使用跑马灯效果显示通知
 */
@Composable
private fun MarqueeCard(marqueeList: List<String>) {
//    CardView {
//        Column(modifier = Modifier.padding(16.dp)) {
    // 标题栏
//            Row(
//                verticalAlignment = Alignment.CenterVertically, modifier = Modifier.fillMaxWidth()
//            ) {
//                Icon(
//                    imageVector = Icons.Default.Info,
//                    contentDescription = "通知",
//                    tint = Primary,
//                    modifier = Modifier.size(20.dp)
//                )
//                Spacer(modifier = Modifier.width(8.dp))
//                Text(
//                    text = stringResource(id = R.string.notifications),
//                    style = MaterialTheme.typography.titleMedium,
//                    color = Color.White,
//                    fontWeight = FontWeight.Bold
//                )
//            }

//            Spacer(modifier = Modifier.height(12.dp))

    // 通知内容区域
    CardView(
        modifier = Modifier
            .fillMaxWidth()
            .clipToBounds()
    ) {
        AndroidView(
            factory = { context ->
                MarqueeView(context).apply {
                    setMaxLines(5)
                    // 确保文本大小适配不同设备
                    setTextGravity(android.view.Gravity.TOP or android.view.Gravity.START)
                }
            }, modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight() // 改为 wrapContentHeight 让高度自适应
                .padding(16.dp), update = { view ->
                view.setData(marqueeList)
                // 强制重新测量和布局
                view.requestLayout()
            })

        // 使用跑马灯组件显示通知
//        MarqueeText(
//            texts = marqueeList.map { it }, displayDuration = 5000, // 5秒
//            maxLines = 5,  // 最多显示5行
//            textColor = Color.White, fontSize = 14.sp
//            // 不再需要lineSpacing参数，因为我们在MarqueeText内部使用lineHeight控制行间距
//        )
    }
//        }
//    }
}

@Composable
private fun Workboard(
    workbenchBean: DashboardBean,
    onShowInfoDialog: (String, String) -> Unit,
    onNavigateToNotification: () -> Unit = {},
    onNavigateToLevel: () -> Unit = {}
) {

    val context = LocalContext.current
    Column {

        // Working time
        CardView {
            Column(
                modifier = Modifier.padding(vertical = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(R.string.working_time),
                    color = Color.White,
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(Modifier.height(15.dp))
                Row {
                    Column(
                        modifier = Modifier.weight(1F),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            stringResource(
                                R.string.mins,
                                workbenchBean.today_data?.working_duration?.toMinutes() ?: 0
                            ), color = Color.White, style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(Modifier.height(5.dp))
                        Text(
                            stringResource(R.string.today),
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                    Column(
                        modifier = Modifier.weight(1F),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            workbenchBean.week_data?.working_duration?.formatCountdown() ?: "00:00",
                            color = Color.White,
                            style = MaterialTheme.typography.bodyLarge
                        )
                        Spacer(Modifier.height(5.dp))
                        Text(
                            stringResource(R.string.week),
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }

        Spacer(Modifier.height(15.dp))

        // Average call duration and Connection Rate
        Row {
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                ) {
                    Text(
                        stringResource(R.string.average_call_duration),
                        color = Color(0xff666666),
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        workbenchBean.week_data?.avg_call_duration?.formatCountdown() ?: "0",
                        color = Color.White,
                        style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(R.string.week),
                        color = Color.Gray,
                        style = MaterialTheme.typography.labelMedium
                    )
                }
            }
            Spacer(Modifier.width(10.dp))
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp)
                ) {
                    Text(
                        stringResource(R.string.connection_rate),
                        color = Color(0xff666666),
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        stringResource(
                            R.string.today_rate_prefix,
                            "${workbenchBean.today_data?.connect_rate?.formatAsPercent()}"
                        ), color = Color.White, style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(
                            R.string.week_rate_prefix,
                            "${workbenchBean.week_data?.connect_rate?.formatAsPercent()}"
                        ), color = Color.Gray, style = MaterialTheme.typography.labelMedium
                    )
                }
            }
        }

        Spacer(Modifier.height(15.dp))

        // Match Call Completion Rate
        Row {
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            onShowInfoDialog(
                                context.getString(R.string.match_call_completion_rate_title),
                                context.getString(
                                    R.string.match_call_completion_rate_tips,
                                    workbenchBean.match_call_effect_duration
                                )
                            )
                        }
                        .padding(16.dp)) {
                    Text(
                        stringResource(R.string.match_call_completion_rate),
                        color = Color(0xff666666),
                        textAlign = TextAlign.Center,
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        stringResource(
                            R.string.today_rate_prefix,
                            "${workbenchBean.today_data?.match_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.White, style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(
                            R.string.total_rate_prefix,
                            "${workbenchBean.total_data?.match_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.Gray, style = MaterialTheme.typography.labelMedium
                    )
                }
            }
            Spacer(Modifier.width(10.dp))
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            onShowInfoDialog(
                                context.getString(R.string.video_call_completion_rate_title),
                                context.getString(
                                    R.string.video_call_completion_rate_tips,
                                    workbenchBean.normal_call_effect_duration
                                )
                            )
                        }
                        .padding(16.dp)) {
                    Text(
                        stringResource(R.string.video_call_completion_rate),
                        textAlign = TextAlign.Center,
                        color = Color(0xff666666),
                        style = MaterialTheme.typography.bodyLarge
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(
                        stringResource(
                            R.string.today_rate_prefix,
                            "${workbenchBean.today_data?.normal_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.White, style = MaterialTheme.typography.labelLarge
                    )
                    Spacer(Modifier.height(5.dp))
                    Text(
                        stringResource(
                            R.string.total_rate_prefix,
                            "${workbenchBean.total_data?.normal_call_effect_rate?.formatAsPercent()}"
                        ), color = Color.Gray, style = MaterialTheme.typography.labelMedium
                    )
                }
            }
        }

        Spacer(Modifier.height(15.dp))

        // Notify and Level
        Row {
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onNavigateToNotification() }
                        .padding(16.dp)) {
                    Image(
                        painter = painterResource(R.mipmap.ic_home_notify),
                        contentDescription = "Icon",
                        modifier = Modifier.size(40.dp)
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(stringResource(R.string.notify), color = Color.White)
                }
            }

            Spacer(Modifier.width(10.dp))
            CardView(modifier = Modifier.weight(1F)) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { onNavigateToLevel() }
                        .padding(16.dp)) {
                    Image(
                        painter = painterResource(R.mipmap.ic_home_level),
                        contentDescription = "Icon",
                        modifier = Modifier.size(40.dp)
                    )
                    Spacer(Modifier.height(10.dp))
                    Text(stringResource(R.string.level), color = Color.White)
                }
            }
        }
    }
}

/**
 * 设置摄像头 - 优化版本，更好地选择预览尺寸
 */
private fun setupCamera(context: Context, callback: (String, Size) -> Unit) {
    try {
        val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val displayMetrics = android.util.DisplayMetrics()
        display.getMetrics(displayMetrics)

        val displayWidth = displayMetrics.widthPixels
        val displayHeight = displayMetrics.heightPixels

        // 遍历摄像头列表，找到前置摄像头
        for (id in cameraManager.cameraIdList) {
            val characteristics = cameraManager.getCameraCharacteristics(id)
            val facing = characteristics.get(CameraCharacteristics.LENS_FACING)
            if (facing == CameraCharacteristics.LENS_FACING_FRONT) {
                // 获取支持的预览尺寸
                val streamConfigurationMap =
                    characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                val previewSizes =
                    streamConfigurationMap?.getOutputSizes(SurfaceTexture::class.java) ?: emptyArray()

                // 使用优化的尺寸选择算法
                val bestSize = CameraPreviewSizeSelector.bestFullScreenSize(
                    previewSizes, displayWidth, displayHeight
                ) ?: getDefaultPreviewSize(previewSizes)

                callback(id, bestSize)
                break
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
        // 提供默认值以防出错
        callback("0", Size(640, 480))
    }
}

/**
 * 获取默认预览尺寸
 */
private fun getDefaultPreviewSize(availableSizes: Array<Size>): Size {
    if (availableSizes.isEmpty()) return Size(640, 480)

    // 按优先级选择默认尺寸
    val preferredSizes = listOf(
        Size(1920, 1080), // 1080p
        Size(1280, 720),  // 720p
        Size(960, 720),   // 4:3 标清
        Size(640, 480)    // VGA
    )

    for (preferred in preferredSizes) {
        availableSizes.firstOrNull { it.width == preferred.width && it.height == preferred.height }?.let {
            return it
        }
    }

    // 如果没有找到首选尺寸，选择分辨率最高的
    return availableSizes.maxByOrNull { it.width * it.height } ?: Size(640, 480)
}

/**
 * 启动摄像头预览
 */
private fun startCameraPreview(
    context: Context,
    cameraManager: CameraManager?,
    textureView: TextureView?,
    cameraId: String,
    previewSize: Size?,
    callback: (CameraDevice, CameraCaptureSession) -> Unit
) {
    if (cameraManager == null || textureView == null || cameraId.isEmpty() || previewSize == null) {
        return
    }

    try {
        if (ContextCompat.checkSelfPermission(
                context, Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            return
        }

        cameraManager.openCamera(cameraId, object : CameraDevice.StateCallback() {
            override fun onOpened(camera: CameraDevice) {
                try {
                    val surfaceTexture = textureView.surfaceTexture
                    surfaceTexture?.setDefaultBufferSize(
                        previewSize.width,
                        previewSize.height
                    )
                    surfaceTexture?.let {
                        val surface = Surface(it)
                        val previewRequestBuilder =
                            camera.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
                        previewRequestBuilder.addTarget(surface)

                        camera.createCaptureSession(
                            listOf(surface), object : CameraCaptureSession.StateCallback() {
                                override fun onConfigured(session: CameraCaptureSession) {
                                    try {
                                        // 确保在开始预览前应用正确的变换
                                        updateTextureViewTransform(textureView, previewSize)
                                        session.setRepeatingRequest(
                                            previewRequestBuilder.build(), null, null
                                        )
                                        callback(camera, session)
                                    } catch (e: Exception) {
                                        e.printStackTrace()
                                    }
                                }

                                override fun onConfigureFailed(session: CameraCaptureSession) {
                                    // 配置失败
                                }
                            }, null
                        )
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onDisconnected(camera: CameraDevice) {
                camera.close()
            }

            override fun onError(camera: CameraDevice, error: Int) {
                camera.close()
            }
        }, null)
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

/**
 * 停止摄像头预览
 */
private fun stopCameraPreview(cameraDevice: CameraDevice?, captureSession: CameraCaptureSession?) {
    try {
        captureSession?.close()
        cameraDevice?.close()
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

/**
 * 更新TextureView的变换矩阵以实现全屏CenterCrop效果 - 使用适配器优化版本
 */
private fun updateTextureViewTransform(
    textureView: TextureView, previewSize: Size, isFrontCamera: Boolean = true
) {
    try {
        // 使用新的适配器计算变换矩阵
        val matrix = CameraDisplayAdapter.calculateTransformMatrix(
            textureView, previewSize, isFrontCamera
        )
        textureView.setTransform(matrix)
    } catch (e: Exception) {
        e.printStackTrace()
        // 如果适配器失败，使用备用方案
        fallbackTransform(textureView, previewSize, isFrontCamera)
    }
}

/**
 * 备用变换方案
 */
private fun fallbackTransform(
    textureView: TextureView, previewSize: Size, isFrontCamera: Boolean = true
) {
    val viewWidth = textureView.width
    val viewHeight = textureView.height

    if (viewWidth == 0 || viewHeight == 0 || previewSize.width == 0 || previewSize.height == 0) {
        return
    }

    val matrix = Matrix()
    val centerX = viewWidth / 2f
    val centerY = viewHeight / 2f

    matrix.reset()

    // 简单的居中缩放
    val scaleX = viewWidth.toFloat() / previewSize.width
    val scaleY = viewHeight.toFloat() / previewSize.height
    val scale = kotlin.math.max(scaleX, scaleY) * 1.01f // 稍微放大以避免黑边

    matrix.postScale(scale, scale, centerX, centerY)

    // 前置摄像头镜像
    if (isFrontCamera) {
        matrix.postScale(-1f, 1f, centerX, centerY)
    }

    textureView.setTransform(matrix)
}



fun getSensorOrientation(context: Context, isFrontCamera: Boolean): Int {
    val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
    var sensorOrientation = 0 // 默认值

    try {
        // 遍历所有可用的摄像头ID
        for (cameraId in cameraManager.cameraIdList) {
            val characteristics = cameraManager.getCameraCharacteristics(cameraId)

            // 获取摄像头朝向 (LENS_FACING)
            val cameraFacing = characteristics.get(CameraCharacteristics.LENS_FACING)

            // 判断是否是前置摄像头或后置摄像头
            if (isFrontCamera && cameraFacing == CameraCharacteristics.LENS_FACING_FRONT) {
                // 找到了前置摄像头
                sensorOrientation =
                    characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION) ?: 0
                break // 找到后即可退出循环
            } else if (!isFrontCamera && cameraFacing == CameraCharacteristics.LENS_FACING_BACK) {
                // 找到了后置摄像头
                sensorOrientation =
                    characteristics.get(CameraCharacteristics.SENSOR_ORIENTATION) ?: 0
                break // 找到后即可退出循环
            }
        }
    } catch (e: Exception) {
        LogX.e("CameraInfo", "Error getting camera characteristics: ${e.message}")
    }
    return sensorOrientation
}

private fun getSurfaceOrientationDegrees(rotation: Int): Int {
    return when (rotation) {
        Surface.ROTATION_0 -> 0
        Surface.ROTATION_90 -> 90
        Surface.ROTATION_180 -> 180
        Surface.ROTATION_270 -> 270
        else -> 0
    }
}

@Preview
@Composable
fun HomeScreenPreview() {
    AnchorTheme {
        Column {
            TaskCard(null) { _, _ ->

            }
            Spacer(Modifier.height(10.dp))
//            SwitchCard()
            Spacer(Modifier.height(10.dp))
//            Workboard()
        }
    }
}
